<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center gap-4 mb-4">
            <flux:button href="{{ route('support.index') }}" variant="ghost" wire:navigate>
                <x-flux::icon icon="arrow-left" class="w-4 h-4 mr-2" />
                Voltar para Central de Ajuda
            </flux:button>
        </div>
        
        <div class="flex items-start justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Ticket #{{ $ticket->ticket_number }}
                </h1>
                <h2 class="text-xl text-gray-700 dark:text-gray-300 mb-4">{{ $ticket->title }}</h2>
                
                <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <span class="flex items-center">
                        <x-flux::icon icon="calendar" class="w-4 h-4 mr-1" />
                        Criado em {{ $ticket->created_at->format('d/m/Y H:i') }}
                    </span>
                    <span class="flex items-center">
                        <x-flux::icon icon="folder" class="w-4 h-4 mr-1" />
                        {{ $ticket->category }}
                    </span>
                    @if($ticket->assignedTo)
                        <span class="flex items-center">
                            <x-flux::icon icon="user" class="w-4 h-4 mr-1" />
                            Atribuído a {{ $ticket->assignedTo->name }}
                        </span>
                    @endif
                </div>
            </div>
            
            <div class="flex items-center gap-3">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                    @if($ticket->status_color === 'blue') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                    @elseif($ticket->status_color === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                    @elseif($ticket->status_color === 'orange') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                    @elseif($ticket->status_color === 'green') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                    @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                    {{ $ticket->status_label }}
                </span>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                    @if($ticket->priority_color === 'green') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                    @elseif($ticket->priority_color === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                    @elseif($ticket->priority_color === 'orange') bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200
                    @elseif($ticket->priority_color === 'red') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                    @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                    Prioridade {{ $ticket->priority_label }}
                </span>
            </div>
        </div>
    </div>

    <!-- Original Message -->
    <flux:card class="mb-6">
        <div class="p-6">
            <div class="flex items-start gap-4">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <x-flux::icon icon="user" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    </div>
                </div>
                <div class="flex-1">
                    <div class="flex items-center gap-2 mb-2">
                        <span class="font-semibold text-gray-900 dark:text-white">{{ $ticket->user->name }}</span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ $ticket->created_at->format('d/m/Y H:i') }}</span>
                    </div>
                    <div class="prose prose-sm max-w-none dark:prose-invert">
                        {!! nl2br(e($ticket->description)) !!}
                    </div>
                    
                    @if($ticket->attachments && count($ticket->attachments) > 0)
                        <div class="mt-4">
                            <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">Anexos:</h4>
                            <div class="space-y-2">
                                @foreach($ticket->attachments as $attachment)
                                    <a href="{{ Storage::url($attachment['path']) }}" 
                                       target="_blank"
                                       class="inline-flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                        <x-flux::icon icon="paper-clip" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                        <span class="text-sm text-gray-700 dark:text-gray-300">{{ $attachment['name'] }}</span>
                                        <span class="text-xs text-gray-500 dark:text-gray-400">({{ number_format($attachment['size'] / 1024, 1) }} KB)</span>
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </flux:card>

    <!-- Messages -->
    @if($messages->count() > 0)
        <div class="space-y-4 mb-6">
            @foreach($messages as $message)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <div class="flex items-start gap-4">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 {{ $message->isFromAdmin() ? 'bg-purple-100 dark:bg-purple-900' : 'bg-blue-100 dark:bg-blue-900' }} rounded-full flex items-center justify-center">
                                    <x-flux::icon 
                                        icon="{{ $message->isFromAdmin() ? 'shield-check' : 'user' }}" 
                                        class="w-5 h-5 {{ $message->isFromAdmin() ? 'text-purple-600 dark:text-purple-400' : 'text-blue-600 dark:text-blue-400' }}" />
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center gap-2 mb-2">
                                    <span class="font-semibold text-gray-900 dark:text-white">{{ $message->user->name }}</span>
                                    @if($message->isFromAdmin())
                                        <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                                            Suporte
                                        </span>
                                    @endif
                                    <span class="text-sm text-gray-500 dark:text-gray-400">{{ $message->created_at->format('d/m/Y H:i') }}</span>
                                </div>
                                <div class="prose prose-sm max-w-none dark:prose-invert">
                                    {!! nl2br(e($message->message)) !!}
                                </div>
                                
                                @if($message->attachments && count($message->attachments) > 0)
                                    <div class="mt-4">
                                        <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">Anexos:</h4>
                                        <div class="space-y-2">
                                            @foreach($message->attachments as $attachment)
                                                <a href="{{ Storage::url($attachment['path']) }}" 
                                                   target="_blank"
                                                   class="inline-flex items-center gap-2 px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                                                    <x-flux::icon icon="paper-clip" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                                                    <span class="text-sm text-gray-700 dark:text-gray-300">{{ $attachment['name'] }}</span>
                                                    <span class="text-xs text-gray-500 dark:text-gray-400">({{ number_format($attachment['size'] / 1024, 1) }} KB)</span>
                                                </a>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <!-- Reply Form -->
    @if($ticket->isOpen())
        <flux:card>
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Adicionar Resposta</h3>
                
                <form wire:submit="sendMessage">
                    <div class="space-y-4">
                        <div>
                            <flux:field>
                                <flux:textarea 
                                    wire:model="newMessage" 
                                    placeholder="Digite sua mensagem..."
                                    rows="4" />
                                <flux:error name="newMessage" />
                            </flux:field>
                        </div>

                        <!-- Attachments -->
                        <div>
                            <flux:field>
                                <flux:label>Anexos (Opcional)</flux:label>
                                <input 
                                    type="file" 
                                    wire:model="attachments" 
                                    multiple
                                    accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.txt"
                                    class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900 dark:file:text-blue-200 dark:hover:file:bg-blue-800" />
                                
                                @if(!empty($attachments))
                                    <div class="mt-3 space-y-2">
                                        @foreach($attachments as $index => $attachment)
                                            <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
                                                <span class="text-sm text-gray-700 dark:text-gray-300">
                                                    {{ $attachment->getClientOriginalName() }}
                                                    <span class="text-gray-500">({{ number_format($attachment->getSize() / 1024, 1) }} KB)</span>
                                                </span>
                                                <flux:button 
                                                    wire:click="removeAttachment({{ $index }})"
                                                    variant="ghost" 
                                                    size="sm"
                                                    class="text-red-600 hover:text-red-700">
                                                    <x-flux::icon icon="x-mark" class="w-4 h-4" />
                                                </flux:button>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </flux:field>
                        </div>

                        <div class="flex justify-end">
                            <flux:button type="submit" variant="primary">
                                <x-flux::icon icon="paper-airplane" class="w-4 h-4 mr-2" />
                                Enviar Resposta
                            </flux:button>
                        </div>
                    </div>
                </form>
            </div>
        </flux:card>
    @endif

    <!-- Rating Section -->
    @if($ticket->canBeRated())
        <div class="mt-6">
            <flux:card class="border-green-200 dark:border-green-800">
                <div class="p-6 bg-green-50 dark:bg-green-900/20">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-green-900 dark:text-green-100 mb-2">Problema Resolvido!</h3>
                            <p class="text-green-800 dark:text-green-200">Como foi nosso atendimento? Sua avaliação nos ajuda a melhorar.</p>
                        </div>
                        <flux:button wire:click="openRatingModal" variant="primary">
                            <x-flux::icon icon="star" class="w-4 h-4 mr-2" />
                            Avaliar Atendimento
                        </flux:button>
                    </div>
                </div>
            </flux:card>
        </div>
    @endif

    <!-- Rating Display -->
    @if($ticket->rating)
        <div class="mt-6">
            <flux:card>
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Sua Avaliação</h3>
                    <div class="flex items-center gap-4">
                        <div class="flex items-center">
                            @for($i = 1; $i <= 5; $i++)
                                <x-flux::icon 
                                    icon="star" 
                                    class="w-5 h-5 {{ $i <= $ticket->rating ? 'text-yellow-400' : 'text-gray-300' }}" 
                                    :solid="$i <= $ticket->rating" />
                            @endfor
                        </div>
                        <span class="text-sm text-gray-600 dark:text-gray-300">{{ $ticket->rating }}/5 estrelas</span>
                    </div>
                    @if($ticket->rating_comment)
                        <p class="mt-3 text-gray-700 dark:text-gray-300">{{ $ticket->rating_comment }}</p>
                    @endif
                </div>
            </flux:card>
        </div>
    @endif

    <!-- Rating Modal -->
    @if($showRatingModal)
        <flux:modal wire:model="showRatingModal" class="max-w-md">
            <flux:modal.header>
                <flux:heading size="lg">Avaliar Atendimento</flux:heading>
            </flux:modal.header>

            <form wire:submit="submitRating">
                <div class="space-y-6">
                    <div>
                        <flux:field>
                            <flux:label>Como você avalia nosso atendimento?</flux:label>
                            <div class="flex items-center gap-2 mt-2">
                                @for($i = 1; $i <= 5; $i++)
                                    <button 
                                        type="button"
                                        wire:click="$set('rating', {{ $i }})"
                                        class="focus:outline-none">
                                        <x-flux::icon 
                                            icon="star" 
                                            class="w-8 h-8 {{ $rating >= $i ? 'text-yellow-400' : 'text-gray-300' }} hover:text-yellow-400 transition-colors" 
                                            :solid="$rating >= $i" />
                                    </button>
                                @endfor
                            </div>
                            <flux:error name="rating" />
                        </flux:field>
                    </div>

                    <div>
                        <flux:field>
                            <flux:label>Comentário (Opcional)</flux:label>
                            <flux:textarea 
                                wire:model="ratingComment" 
                                placeholder="Conte-nos mais sobre sua experiência..."
                                rows="3" />
                            <flux:error name="ratingComment" />
                        </flux:field>
                    </div>
                </div>

                <flux:modal.footer>
                    <div class="flex gap-2">
                        <flux:button wire:click="closeRatingModal" variant="ghost">
                            Cancelar
                        </flux:button>
                        <flux:button type="submit" variant="primary">
                            <x-flux::icon icon="star" class="w-4 h-4 mr-2" />
                            Enviar Avaliação
                        </flux:button>
                    </div>
                </flux:modal.footer>
            </form>
        </flux:modal>
    @endif
</div>
